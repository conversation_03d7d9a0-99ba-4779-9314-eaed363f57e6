/**
 * 视觉脚本优化器
 * 提供视觉脚本的性能优化功能，包括节点缓存、懒加载和批处理
 */
import { Graph } from '../graph/Graph';
import { Node } from '../nodes/Node';
import { FunctionNode } from '../nodes/FunctionNode';

/**
 * 缓存策略
 */
export enum CacheStrategy {
  /**
   * 不缓存
   */
  NONE = 'none',
  
  /**
   * 缓存所有节点
   */
  ALL = 'all',
  
  /**
   * 智能缓存（根据节点类型和使用频率）
   */
  SMART = 'smart'
}

/**
 * 缓存项
 */
interface CacheItem {
  /**
   * 节点ID
   */
  nodeId: string;
  
  /**
   * 缓存值
   */
  value: any;
  
  /**
   * 最后更新时间
   */
  lastUpdated: number;
  
  /**
   * 使用次数
   */
  useCount: number;
  
  /**
   * 输入值哈希
   */
  inputHash: string;
}

/**
 * 批处理组
 */
interface BatchGroup {
  /**
   * 节点列表
   */
  nodes: Node[];
  
  /**
   * 优先级
   */
  priority: number;
}

/**
 * 视觉脚本优化器配置
 */
export interface VisualScriptOptimizerConfig {
  /**
   * 缓存策略
   */
  cacheStrategy: CacheStrategy;
  
  /**
   * 最大缓存项数量
   */
  maxCacheItems: number;
  
  /**
   * 缓存过期时间（毫秒）
   */
  cacheExpirationTime: number;
  
  /**
   * 是否启用懒加载
   */
  enableLazyLoading: boolean;
  
  /**
   * 懒加载视图范围
   */
  lazyLoadingViewRange: number;
  
  /**
   * 是否启用批处理
   */
  enableBatching: boolean;
  
  /**
   * 批处理大小
   */
  batchSize: number;
}

/**
 * 视觉脚本优化器
 */
export class VisualScriptOptimizer {
  /**
   * 配置
   */
  private config: VisualScriptOptimizerConfig;
  
  /**
   * 节点缓存
   */
  private nodeCache: Map<string, CacheItem> = new Map();
  
  /**
   * 批处理组
   */
  private batchGroups: BatchGroup[] = [];
  
  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config?: Partial<VisualScriptOptimizerConfig>) {
    this.config = {
      cacheStrategy: CacheStrategy.SMART,
      maxCacheItems: 1000,
      cacheExpirationTime: 30000, // 30秒
      enableLazyLoading: true,
      lazyLoadingViewRange: 1000,
      enableBatching: true,
      batchSize: 10,
      ...config
    };
  }
  
  /**
   * 优化图
   * @param graph 视觉脚本图
   */
  public optimizeGraph(graph: Graph): void {
    // 根据配置应用优化
    if (this.config.cacheStrategy !== CacheStrategy.NONE) {
      this.setupNodeCaching(graph);
    }
    
    if (this.config.enableBatching) {
      this.setupBatching(graph);
    }
    
    if (this.config.enableLazyLoading) {
      this.setupLazyLoading(graph);
    }
  }
  
  /**
   * 设置节点缓存
   * @param graph 视觉脚本图
   */
  private setupNodeCaching(graph: Graph): void {
    // 遍历图中的所有节点
    for (const node of graph.getNodes()) {
      // 只对函数节点应用缓存
      if (node instanceof FunctionNode) {
        // 保存原始执行方法
        const originalExecute = node.execute.bind(node);
        
        // 重写执行方法，添加缓存逻辑
        node.execute = () => {
          // 检查是否应该缓存该节点
          if (!this.shouldCacheNode(node)) {
            return originalExecute();
          }
          
          // 计算输入值哈希
          const inputHash = this.calculateInputHash(node);
          
          // 检查缓存
          const cacheKey = `${graph.id}:${node.id}:${inputHash}`;
          const cachedItem = this.nodeCache.get(cacheKey);
          
          if (cachedItem && !this.isCacheExpired(cachedItem)) {
            // 更新使用计数
            cachedItem.useCount++;
            return cachedItem.value;
          }
          
          // 执行节点
          const result = originalExecute();
          
          // 缓存结果
          this.cacheNodeResult(cacheKey, node.id, result, inputHash);
          
          return result;
        };
      }
    }
  }
  
  /**
   * 设置批处理
   * @param graph 视觉脚本图
   */
  private setupBatching(graph: Graph): void {
    // 分析节点依赖关系
    const dependencies = this.analyzeDependencies(graph);
    
    // 创建批处理组
    this.createBatchGroups(graph, dependencies);
    
    // 设置批处理执行
    graph.on('beforeExecute', this.executeBatches.bind(this));
  }
  
  /**
   * 设置懒加载
   * @param graph 视觉脚本图
   */
  private setupLazyLoading(graph: Graph): void {
    // 实现懒加载逻辑
    graph.on('viewportChanged', (viewport: { x: number, y: number, width: number, height: number }) => {
      // 获取视图范围内的节点
      const visibleNodes = this.getNodesInViewport(graph, viewport);
      
      // 加载可见节点
      this.loadNodes(visibleNodes);
      
      // 卸载不可见节点
      this.unloadNodes(graph, visibleNodes);
    });
  }
  
  /**
   * 判断是否应该缓存节点
   * @param node 节点
   * @returns 是否应该缓存
   */
  private shouldCacheNode(node: Node): boolean {
    // 根据缓存策略判断
    switch (this.config.cacheStrategy) {
      case CacheStrategy.ALL:
        return true;
      
      case CacheStrategy.SMART:
        // 智能缓存策略
        // 1. 计算密集型节点应该缓存
        // 2. 频繁使用的节点应该缓存
        // 3. 输入不经常变化的节点应该缓存
        return node.getMetadata().computeIntensive || 
               node.getMetadata().frequentlyUsed ||
               !node.getMetadata().inputsFrequentlyChange;
      
      default:
        return false;
    }
  }
  
  /**
   * 计算输入哈希
   * @param node 节点
   * @returns 输入哈希
   */
  private calculateInputHash(node: Node): string {
    // 获取所有输入值
    const inputs: { [key: string]: any } = {};
    
    for (const socket of node.getInputSockets()) {
      inputs[socket.name] = node.getInputValue(socket.name);
    }
    
    // 计算哈希
    return JSON.stringify(inputs);
  }
  
  /**
   * 判断缓存是否过期
   * @param cacheItem 缓存项
   * @returns 是否过期
   */
  private isCacheExpired(cacheItem: CacheItem): boolean {
    const now = Date.now();
    return now - cacheItem.lastUpdated > this.config.cacheExpirationTime;
  }
  
  /**
   * 缓存节点结果
   * @param cacheKey 缓存键
   * @param nodeId 节点ID
   * @param value 结果值
   * @param inputHash 输入哈希
   */
  private cacheNodeResult(cacheKey: string, nodeId: string, value: any, inputHash: string): void {
    // 检查缓存大小
    if (this.nodeCache.size >= this.config.maxCacheItems) {
      // 清理最不常用的缓存项
      this.cleanCache();
    }
    
    // 添加缓存项
    this.nodeCache.set(cacheKey, {
      nodeId,
      value,
      lastUpdated: Date.now(),
      useCount: 1,
      inputHash
    });
  }
  
  /**
   * 清理缓存
   */
  private cleanCache(): void {
    // 按使用次数排序
    const sortedItems = Array.from(this.nodeCache.entries())
      .sort((a, b) => a[1].useCount - b[1].useCount);
    
    // 移除最不常用的25%
    const removeCount = Math.ceil(this.nodeCache.size * 0.25);
    
    for (let i = 0; i < removeCount; i++) {
      if (sortedItems[i]) {
        this.nodeCache.delete(sortedItems[i][0]);
      }
    }
  }
  
  /**
   * 分析依赖关系
   * @param graph 视觉脚本图
   * @returns 依赖关系
   */
  private analyzeDependencies(graph: Graph): Map<string, string[]> {
    const dependencies = new Map<string, string[]>();
    
    // 遍历所有节点
    for (const node of graph.getNodes()) {
      const nodeDependencies: string[] = [];
      
      // 获取输入连接的节点
      for (const socket of node.getInputSockets()) {
        const connection = graph.getConnectionToInput(node.id, socket.name);
        
        if (connection) {
          nodeDependencies.push(connection.outputNodeId);
        }
      }
      
      dependencies.set(node.id, nodeDependencies);
    }
    
    return dependencies;
  }
  
  /**
   * 创建批处理组
   * @param graph 视觉脚本图
   * @param dependencies 依赖关系
   */
  private createBatchGroups(graph: Graph, dependencies: Map<string, string[]>): void {
    // 清空批处理组
    this.batchGroups = [];
    
    // 计算节点层级
    const nodeLevels = this.calculateNodeLevels(graph, dependencies);
    
    // 按层级分组
    const levelGroups = new Map<number, Node[]>();
    
    for (const [nodeId, level] of nodeLevels.entries()) {
      const node = graph.getNode(nodeId);
      
      if (node) {
        if (!levelGroups.has(level)) {
          levelGroups.set(level, []);
        }
        
        levelGroups.get(level)!.push(node);
      }
    }
    
    // 创建批处理组
    for (const [level, nodes] of levelGroups.entries()) {
      // 按批处理大小分组
      for (let i = 0; i < nodes.length; i += this.config.batchSize) {
        const batchNodes = nodes.slice(i, i + this.config.batchSize);
        
        this.batchGroups.push({
          nodes: batchNodes,
          priority: level
        });
      }
    }
    
    // 按优先级排序
    this.batchGroups.sort((a, b) => a.priority - b.priority);
  }
  
  /**
   * 计算节点层级
   * @param graph 视觉脚本图
   * @param dependencies 依赖关系
   * @returns 节点层级
   */
  private calculateNodeLevels(graph: Graph, dependencies: Map<string, string[]>): Map<string, number> {
    const levels = new Map<string, number>();
    
    // 初始化所有节点层级为0
    for (const node of graph.getNodes()) {
      levels.set(node.id, 0);
    }
    
    // 计算层级
    let changed = true;
    
    while (changed) {
      changed = false;
      
      for (const [nodeId, deps] of dependencies.entries()) {
        const currentLevel = levels.get(nodeId) || 0;
        
        // 计算依赖节点的最大层级
        let maxDependencyLevel = 0;
        
        for (const depId of deps) {
          const depLevel = levels.get(depId) || 0;
          maxDependencyLevel = Math.max(maxDependencyLevel, depLevel);
        }
        
        // 更新层级
        const newLevel = deps.length > 0 ? maxDependencyLevel + 1 : 0;
        
        if (newLevel !== currentLevel) {
          levels.set(nodeId, newLevel);
          changed = true;
        }
      }
    }
    
    return levels;
  }
  
  /**
   * 执行批处理
   */
  private executeBatches(): void {
    // 执行所有批处理组
    for (const group of this.batchGroups) {
      // 并行执行批处理组中的节点
      // 注意：这里使用Promise.all是为了概念上的并行
      // 实际上JavaScript是单线程的，除非使用Web Worker
      Promise.all(group.nodes.map(node => {
        // 只执行没有被执行过的节点
        if (!node.isExecuted()) {
          return Promise.resolve(node.execute());
        }
        return Promise.resolve();
      }));
    }
  }
  
  /**
   * 获取视图范围内的节点
   * @param graph 视觉脚本图
   * @param viewport 视图范围
   * @returns 视图范围内的节点
   */
  private getNodesInViewport(graph: Graph, viewport: { x: number, y: number, width: number, height: number }): Set<string> {
    const visibleNodes = new Set<string>();
    
    // 扩展视图范围
    const extendedViewport = {
      x: viewport.x - this.config.lazyLoadingViewRange,
      y: viewport.y - this.config.lazyLoadingViewRange,
      width: viewport.width + this.config.lazyLoadingViewRange * 2,
      height: viewport.height + this.config.lazyLoadingViewRange * 2
    };
    
    // 检查每个节点是否在扩展视图范围内
    for (const node of graph.getNodes()) {
      const position = node.getPosition();
      
      if (position.x >= extendedViewport.x && 
          position.x <= extendedViewport.x + extendedViewport.width &&
          position.y >= extendedViewport.y && 
          position.y <= extendedViewport.y + extendedViewport.height) {
        visibleNodes.add(node.id);
      }
    }
    
    return visibleNodes;
  }
  
  /**
   * 加载节点
   * @param nodeIds 节点ID列表
   */
  private loadNodes(nodeIds: Set<string>): void {
    // 实现节点加载逻辑
    // 这里可以加载节点的资源、初始化节点等
  }
  
  /**
   * 卸载节点
   * @param graph 视觉脚本图
   * @param visibleNodeIds 可见节点ID列表
   */
  private unloadNodes(graph: Graph, visibleNodeIds: Set<string>): void {
    // 获取所有不可见的节点
    const invisibleNodes = new Set<string>();
    
    for (const node of graph.getNodes()) {
      if (!visibleNodeIds.has(node.id)) {
        invisibleNodes.add(node.id);
      }
    }
    
    // 实现节点卸载逻辑
    // 这里可以释放节点的资源、缓存节点状态等
  }
  
  /**
   * 获取性能统计信息
   * @returns 性能统计信息
   */
  public getPerformanceStats(): any {
    return {
      cacheSize: this.nodeCache.size,
      cacheHitRate: this.calculateCacheHitRate(),
      batchGroupCount: this.batchGroups.length,
      config: this.config
    };
  }
  
  /**
   * 计算缓存命中率
   * @returns 缓存命中率
   */
  private calculateCacheHitRate(): number {
    // 实现缓存命中率计算逻辑
    return 0;
  }
}
