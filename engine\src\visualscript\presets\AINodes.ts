/**
 * 视觉脚本AI节点
 * 提供AI动画合成和自然语言处理相关的节点
 */
import type { Entity } from '../../core/Entity';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { AIAnimationSynthesisSystem } from '../../animation/AIAnimationSynthesisSystem';

// 导入其他AI节点模块
import { registerAIModelNodes } from './AIModelNodes';
import { registerAIEmotionNodes } from './AIEmotionNodes';
import { registerAINLPNodes } from './AINLPNodes';

/**
 * 生成身体动画节点
 * 使用AI生成身体动画
 */
export class GenerateBodyAnimationNode extends AsyncNode {
  /** 请求ID */
  private requestId: string | null = null;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'prompt',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '提示文本',
      defaultValue: '走路'
    });

    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '持续时间（秒）',
      defaultValue: 5.0
    });

    this.addInput({
      name: 'loop',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否循环',
      defaultValue: false
    });

    this.addInput({
      name: 'style',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '动画风格',
      defaultValue: 'natural',
      optional: true
    });

    this.addInput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '动画强度',
      defaultValue: 1.0,
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '生成成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '生成失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'animationClip',
      type: SocketType.DATA,
      dataType: 'AnimationClip',
      direction: SocketDirection.OUTPUT,
      description: '生成的动画片段'
    });

    this.addOutput({
      name: 'generationTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '生成时间（毫秒）'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const prompt = this.getInputValue('prompt') as string;
    const duration = this.getInputValue('duration') as number;
    const loop = this.getInputValue('loop') as boolean;
    const style = this.getInputValue('style') as string;
    const intensity = this.getInputValue('intensity') as number;

    // 检查输入值是否有效
    if (!entity || !prompt) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取AI动画合成系统
    const aiAnimationSystem = this.context.world.getSystem(AIAnimationSynthesisSystem);
    if (!aiAnimationSystem) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 生成身体动画
      this.requestId = aiAnimationSystem.generateBodyAnimation(entity, prompt, duration, {
        loop,
        style,
        intensity
      });

      if (!this.requestId) {
        this.triggerFlow('fail');
        return false;
      }

      // 获取AI动画合成组件
      const component = aiAnimationSystem.getAIAnimationSynthesis(entity);
      if (!component) {
        this.triggerFlow('fail');
        return false;
      }

      // 等待动画生成完成
      const result = await this.waitForAnimationResult(component, this.requestId);

      if (result && result.success && result.clip) {
        // 设置输出值
        this.setOutputValue('animationClip', result.clip);
        this.setOutputValue('generationTime', result.generationTime);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }

  /**
   * 等待动画生成结果
   * @param component AI动画合成组件
   * @param requestId 请求ID
   * @returns 生成结果
   */
  private async waitForAnimationResult(component: any, requestId: string): Promise<any> {
    const maxWaitTime = 30000; // 最大等待时间30秒
    const pollInterval = 100; // 轮询间隔100毫秒
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      const result = component.getResult(requestId);
      if (result) {
        return result;
      }

      // 等待一段时间后再次检查
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }

    // 超时返回null
    return null;
  }

  /**
   * 取消执行
   */
  public cancel(): void {
    if (this.requestId) {
      // 获取AI动画合成系统
      const aiAnimationSystem = this.context.world.getSystem(AIAnimationSynthesisSystem);
      if (aiAnimationSystem) {
        // 取消请求
        const component = aiAnimationSystem.getAIAnimationSynthesis(this.getInputValue('entity') as Entity);
        if (component) {
          component.cancelRequest(this.requestId);
        }
      }

      this.requestId = null;
    }
  }
}

/**
 * 生成面部动画节点
 * 使用AI生成面部动画
 */
export class GenerateFacialAnimationNode extends AsyncNode {
  /** 请求ID */
  private requestId: string | null = null;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'prompt',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '提示文本',
      defaultValue: '你好，世界！'
    });

    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '持续时间（秒）',
      defaultValue: 3.0
    });

    this.addInput({
      name: 'loop',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否循环',
      defaultValue: false
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '生成成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '生成失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'facialAnimationClip',
      type: SocketType.DATA,
      dataType: 'FacialAnimationClip',
      direction: SocketDirection.OUTPUT,
      description: '生成的面部动画片段'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const prompt = this.getInputValue('prompt') as string;
    const duration = this.getInputValue('duration') as number;
    const loop = this.getInputValue('loop') as boolean;

    // 检查输入值是否有效
    if (!entity || !prompt) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取AI动画合成系统
    const aiAnimationSystem = this.context.world.getSystem(AIAnimationSynthesisSystem);
    if (!aiAnimationSystem) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 生成面部动画
      this.requestId = aiAnimationSystem.generateFacialAnimation(entity, prompt, duration, {
        loop
      });

      if (!this.requestId) {
        this.triggerFlow('fail');
        return false;
      }

      // 获取AI动画合成组件
      const component = aiAnimationSystem.getAIAnimationSynthesis(entity);
      if (!component) {
        this.triggerFlow('fail');
        return false;
      }

      // 等待动画生成完成
      const result = await this.waitForAnimationResult(component, this.requestId);

      if (result && result.success && result.clip) {
        // 设置输出值
        this.setOutputValue('facialAnimationClip', result.clip);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }

  /**
   * 等待动画生成结果
   * @param component AI动画合成组件
   * @param requestId 请求ID
   * @returns 生成结果
   */
  private async waitForAnimationResult(component: any, requestId: string): Promise<any> {
    const maxWaitTime = 30000; // 最大等待时间30秒
    const pollInterval = 100; // 轮询间隔100毫秒
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      const result = component.getResult(requestId);
      if (result) {
        return result;
      }

      // 等待一段时间后再次检查
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }

    // 超时返回null
    return null;
  }

  /**
   * 取消执行
   */
  public cancel(): void {
    if (this.requestId) {
      // 获取AI动画合成系统
      const aiAnimationSystem = this.context.world.getSystem(AIAnimationSynthesisSystem);
      if (aiAnimationSystem) {
        // 取消请求
        const component = aiAnimationSystem.getAIAnimationSynthesis(this.getInputValue('entity') as Entity);
        if (component) {
          component.cancelRequest(this.requestId);
        }
      }

      this.requestId = null;
    }
  }
}

/**
 * 注册AI节点
 * @param registry 节点注册表
 */
export function registerAINodes(registry: NodeRegistry): void {
  // 注册生成身体动画节点
  registry.registerNodeType({
    type: 'ai/animation/generateBodyAnimation',
    category: NodeCategory.AI,
    constructor: GenerateBodyAnimationNode,
    label: '生成身体动画',
    description: '使用AI生成身体动画',
    icon: 'body',
    color: '#673AB7',
    tags: ['ai', 'animation', 'body']
  });

  // 注册生成面部动画节点
  registry.registerNodeType({
    type: 'ai/animation/generateFacialAnimation',
    category: NodeCategory.AI,
    constructor: GenerateFacialAnimationNode,
    label: '生成面部动画',
    description: '使用AI生成面部动画',
    icon: 'face',
    color: '#673AB7',
    tags: ['ai', 'animation', 'facial']
  });

  // 注册AI模型节点
  registerAIModelNodes(registry);

  // 注册AI情感节点
  registerAIEmotionNodes(registry);

  // 注册AI自然语言处理节点
  registerAINLPNodes(registry);

  console.log('已注册所有AI节点类型');
}
