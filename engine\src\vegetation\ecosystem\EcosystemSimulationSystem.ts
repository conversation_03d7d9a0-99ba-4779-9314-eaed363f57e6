/**
 * 生态系统模拟系统
 * 用于模拟生态系统中的植被分布规则
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import { Debug } from '../../utils/Debug';
import { EventEmitter } from '../../utils/EventEmitter';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';
import { VegetationComponent } from '../components/VegetationComponent';
import { TerrainComponent } from '../../terrain/components/TerrainComponent';

/**
 * 生态系统模拟系统配置
 */
export interface EcosystemSimulationSystemConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
  /** 是否使用生态系统网格 */
  useEcosystemGrid?: boolean;
  /** 生态系统网格分辨率 */
  ecosystemGridResolution?: number;
  /** 是否使用季节变化 */
  useSeasonalChanges?: boolean;
  /** 是否使用植被竞争 */
  useVegetationCompetition?: boolean;
  /** 是否使用植被生长 */
  useVegetationGrowth?: boolean;
}

/**
 * 生态系统模拟系统事件类型
 */
export enum EcosystemSimulationSystemEventType {
  /** 生态系统更新 */
  ECOSYSTEM_UPDATED = 'ecosystem_updated',
  /** 植被分布更新 */
  VEGETATION_DISTRIBUTION_UPDATED = 'vegetation_distribution_updated',
  /** 季节变化 */
  SEASON_CHANGED = 'season_changed'
}

/**
 * 植被类型
 */
export enum VegetationType {
  /** 树木 */
  TREE = 'tree',
  /** 灌木 */
  SHRUB = 'shrub',
  /** 草地 */
  GRASS = 'grass',
  /** 花卉 */
  FLOWER = 'flower',
  /** 水生植物 */
  AQUATIC = 'aquatic',
  /** 季节性植物 */
  SEASONAL = 'seasonal',
  /** 自定义植物 */
  CUSTOM = 'custom'
}

/**
 * 植被生长阶段
 */
export enum VegetationGrowthStage {
  /** 种子 */
  SEED = 'seed',
  /** 幼苗 */
  SEEDLING = 'seedling',
  /** 幼年 */
  JUVENILE = 'juvenile',
  /** 成年 */
  ADULT = 'adult',
  /** 老年 */
  SENIOR = 'senior',
  /** 死亡 */
  DEAD = 'dead'
}

/**
 * 季节类型
 */
export enum SeasonType {
  /** 春季 */
  SPRING = 'spring',
  /** 夏季 */
  SUMMER = 'summer',
  /** 秋季 */
  AUTUMN = 'autumn',
  /** 冬季 */
  WINTER = 'winter'
}

/**
 * 植被数据
 */
export interface VegetationData {
  /** 植被类型 */
  type: VegetationType;
  /** 生长阶段 */
  growthStage: VegetationGrowthStage;
  /** 年龄 */
  age: number;
  /** 健康度 */
  health: number;
  /** 竞争力 */
  competitiveness: number;
  /** 耐旱性 */
  droughtTolerance: number;
  /** 耐寒性 */
  coldTolerance: number;
  /** 耐热性 */
  heatTolerance: number;
  /** 耐湿性 */
  wetTolerance: number;
  /** 光照需求 */
  lightRequirement: number;
  /** 营养需求 */
  nutrientRequirement: number;
  /** 生长速度 */
  growthRate: number;
  /** 最大寿命 */
  maxAge: number;
  /** 最大高度 */
  maxHeight: number;
  /** 最大宽度 */
  maxWidth: number;
  /** 当前高度 */
  currentHeight: number;
  /** 当前宽度 */
  currentWidth: number;
  /** 是否常绿 */
  evergreen: boolean;
  /** 是否开花 */
  flowering: boolean;
  /** 开花季节 */
  floweringSeason: SeasonType[];
  /** 是否结果 */
  fruiting: boolean;
  /** 结果季节 */
  fruitingSeason: SeasonType[];
  /** 是否落叶 */
  deciduous: boolean;
  /** 落叶季节 */
  leafFallSeason: SeasonType[];
  /** 自定义数据 */
  userData?: any;
}

/**
 * 生态系统网格单元
 */
export interface EcosystemGridCell {
  /** 位置 */
  position: THREE.Vector3;
  /** 大小 */
  size: THREE.Vector3;
  /** 植被数据列表 */
  vegetationData: VegetationData[];
  /** 土壤湿度 */
  soilMoisture: number;
  /** 土壤肥力 */
  soilFertility: number;
  /** 光照强度 */
  lightIntensity: number;
  /** 温度 */
  temperature: number;
  /** 高度 */
  elevation: number;
  /** 坡度 */
  slope: number;
  /** 坡向 */
  aspect: number;
  /** 是否水域 */
  isWater: boolean;
  /** 自定义数据 */
  userData?: any;
}

/**
 * 生态系统模拟系统
 */
export class EcosystemSimulationSystem extends System {
  /** 系统类型 */
  public static readonly TYPE: string = 'EcosystemSimulationSystem';

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: EcosystemSimulationSystemConfig = {
    enabled: true,
    autoUpdate: true,
    updateFrequency: 10,
    useDebugVisualization: false,
    useEcosystemGrid: true,
    ecosystemGridResolution: 32,
    useSeasonalChanges: true,
    useVegetationCompetition: true,
    useVegetationGrowth: true
  };

  /** 配置 */
  private config: EcosystemSimulationSystemConfig;

  /** 是否系统启用 */
  private systemEnabled: boolean;

  /** 是否自动更新 */
  private autoUpdate: boolean;

  /** 更新频率 */
  private updateFrequency: number;

  /** 帧计数器 */
  private frameCount: number = 0;

  /** 是否使用调试可视化 */
  private useDebugVisualization: boolean;

  /** 是否使用生态系统网格 */
  private useEcosystemGrid: boolean;

  /** 生态系统网格分辨率 */
  private ecosystemGridResolution: number;

  /** 是否使用季节变化 */
  private useSeasonalChanges: boolean;

  /** 是否使用植被竞争 */
  private useVegetationCompetition: boolean;

  /** 是否使用植被生长 */
  private useVegetationGrowth: boolean;

  /** 生态系统网格 */
  private ecosystemGrid: EcosystemGridCell[][] | null = null;

  /** 植被组件列表 */
  private vegetationComponents: Map<Entity, VegetationComponent> = new Map();

  /** 地形组件列表 */
  private terrainComponents: Map<Entity, TerrainComponent> = new Map();

  /** 当前季节 */
  private currentSeason: SeasonType = SeasonType.SUMMER;

  /** 季节变化计时器 */
  private seasonChangeTimer: number = 0;

  /** 季节持续时间（秒） */
  private seasonDuration: number = 300;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 性能监控器 */
  private performanceMonitor: PerformanceMonitor = PerformanceMonitor.getInstance();

  /** 调试网格列表 */
  private debugMeshes: THREE.Mesh[] = [];

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config?: EcosystemSimulationSystemConfig) {
    super(0); // 优先级为0
    this.world = world;

    // 合并配置
    this.config = { ...EcosystemSimulationSystem.DEFAULT_CONFIG, ...config };

    // 设置属性
    this.systemEnabled = this.config.enabled !== undefined ? this.config.enabled : true;
    this.autoUpdate = this.config.autoUpdate !== undefined ? this.config.autoUpdate : true;
    this.updateFrequency = this.config.updateFrequency || 10;
    this.useDebugVisualization = this.config.useDebugVisualization !== undefined ? this.config.useDebugVisualization : false;
    this.useEcosystemGrid = this.config.useEcosystemGrid !== undefined ? this.config.useEcosystemGrid : true;
    this.ecosystemGridResolution = this.config.ecosystemGridResolution || 32;
    this.useSeasonalChanges = this.config.useSeasonalChanges !== undefined ? this.config.useSeasonalChanges : true;
    this.useVegetationCompetition = this.config.useVegetationCompetition !== undefined ? this.config.useVegetationCompetition : true;
    this.useVegetationGrowth = this.config.useVegetationGrowth !== undefined ? this.config.useVegetationGrowth : true;

    // 初始化柏林噪声
    this.initPerlinNoise();
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return EcosystemSimulationSystem.TYPE;
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    super.initialize();

    // 初始化生态系统网格
    if (this.useEcosystemGrid) {
      this.initEcosystemGrid();
    }

    // 监听实体添加和移除事件
    this.world.on('entityAdded', this.onEntityAdded.bind(this));
    this.world.on('entityRemoved', this.onEntityRemoved.bind(this));

    // 初始化现有实体
    this.world.getEntities().forEach(entity => {
      if (entity.hasComponent(VegetationComponent.TYPE)) {
        this.addVegetationComponent(entity, entity.getComponent(VegetationComponent.TYPE) as VegetationComponent);
      }
      if (entity.hasComponent(TerrainComponent.TYPE)) {
        this.addTerrainComponent(entity, entity.getComponent(TerrainComponent.TYPE) as TerrainComponent);
      }
    });

    // 初始化调试可视化
    if (this.useDebugVisualization) {
      this.initDebugVisualization();
    }

    Debug.log('EcosystemSimulationSystem', '生态系统模拟系统初始化完成');
  }

  /**
   * 销毁系统
   */
  public destroy(): void {
    // 清除调试网格
    this.clearDebugMeshes();

    // 清除生态系统网格
    this.ecosystemGrid = null;

    // 清除植被组件
    this.vegetationComponents.clear();

    // 清除地形组件
    this.terrainComponents.clear();

    super.dispose();
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.systemEnabled || !this.autoUpdate) {
      return;
    }

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.updateFrequency !== 0) {
      return;
    }

    // 如果启用了性能监控，开始计时
    if (this.useDebugVisualization) {
      this.performanceMonitor.beginMeasure('ecosystemUpdate');
    }

    // 更新季节
    if (this.useSeasonalChanges) {
      this.updateSeason(deltaTime);
    }

    // 更新生态系统网格
    if (this.useEcosystemGrid && this.ecosystemGrid) {
      this.updateEcosystemGrid(deltaTime);
    }

    // 如果启用了调试可视化，更新调试可视化
    if (this.useDebugVisualization) {
      this.updateDebugVisualization();
      this.performanceMonitor.endMeasure('ecosystemUpdate');
    }

    // 发出生态系统更新事件
    this.eventEmitter.emit(EcosystemSimulationSystemEventType.ECOSYSTEM_UPDATED);
  }

  /**
   * 实体添加事件处理
   * @param entity 实体
   */
  private onEntityAdded(entity: Entity): void {
    if (entity.hasComponent(VegetationComponent.TYPE)) {
      this.addVegetationComponent(entity, entity.getComponent(VegetationComponent.TYPE) as VegetationComponent);
    }
    if (entity.hasComponent(TerrainComponent.TYPE)) {
      this.addTerrainComponent(entity, entity.getComponent(TerrainComponent.TYPE) as TerrainComponent);
    }
  }

  /**
   * 实体移除事件处理
   * @param entity 实体
   */
  private onEntityRemoved(entity: Entity): void {
    if (entity.hasComponent(VegetationComponent.TYPE)) {
      this.removeVegetationComponent(entity);
    }
    if (entity.hasComponent(TerrainComponent.TYPE)) {
      this.removeTerrainComponent(entity);
    }
  }

  /**
   * 添加植被组件
   * @param entity 实体
   * @param component 植被组件
   */
  public addVegetationComponent(entity: Entity, component: VegetationComponent): void {
    this.vegetationComponents.set(entity, component);
  }

  /**
   * 移除植被组件
   * @param entity 实体
   */
  public removeVegetationComponent(entity: Entity): void {
    this.vegetationComponents.delete(entity);
  }

  /**
   * 添加地形组件
   * @param entity 实体
   * @param component 地形组件
   */
  public addTerrainComponent(entity: Entity, component: TerrainComponent): void {
    this.terrainComponents.set(entity, component);
  }

  /**
   * 移除地形组件
   * @param entity 实体
   */
  public removeTerrainComponent(entity: Entity): void {
    this.terrainComponents.delete(entity);
  }

  /**
   * 初始化生态系统网格
   */
  private initEcosystemGrid(): void {
    // 获取地形组件
    if (this.terrainComponents.size === 0) {
      Debug.warn('EcosystemSimulationSystem', '找不到地形组件，无法初始化生态系统网格');
      return;
    }

    // 获取第一个地形组件
    const terrainComponent = this.terrainComponents.values().next().value;
    const terrainWidth = terrainComponent.width;
    const terrainHeight = terrainComponent.height;

    // 创建生态系统网格
    const resolution = this.ecosystemGridResolution;
    const cellWidth = terrainWidth / resolution;
    const cellHeight = terrainHeight / resolution;

    // 创建二维数组
    this.ecosystemGrid = new Array(resolution);
    for (let i = 0; i < resolution; i++) {
      this.ecosystemGrid[i] = new Array(resolution);
      for (let j = 0; j < resolution; j++) {
        // 计算网格单元位置
        const x = (i * cellWidth) - (terrainWidth / 2) + (cellWidth / 2);
        const z = (j * cellHeight) - (terrainHeight / 2) + (cellHeight / 2);
        const y = terrainComponent.getHeight(x, z);

        // 获取地形法线
        const normal = this.getTerrainNormal(terrainComponent, x, z);
        const slope = this.calculateSlope(normal);
        const aspect = this.calculateAspect(normal);

        // 创建网格单元
        this.ecosystemGrid[i][j] = {
          position: new THREE.Vector3(x, y, z),
          size: new THREE.Vector3(cellWidth, 1, cellHeight),
          vegetationData: [],
          soilMoisture: this.calculateSoilMoisture(x, z, y, terrainComponent),
          soilFertility: this.calculateSoilFertility(x, z, y, terrainComponent),
          lightIntensity: this.calculateLightIntensity(x, z, y, normal),
          temperature: this.calculateTemperature(x, z, y, this.currentSeason),
          elevation: y,
          slope,
          aspect,
          isWater: y < 0, // 简单判断水域
          userData: {}
        };
      }
    }

    Debug.log('EcosystemSimulationSystem', `生态系统网格初始化完成，分辨率: ${resolution}x${resolution}`);
  }

  /**
   * 更新生态系统网格
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateEcosystemGrid(deltaTime: number): void {
    if (!this.ecosystemGrid) {
      return;
    }

    // 获取地形组件
    if (this.terrainComponents.size === 0) {
      return;
    }

    // 更新网格单元
    const resolution = this.ecosystemGridResolution;
    for (let i = 0; i < resolution; i++) {
      for (let j = 0; j < resolution; j++) {
        const cell = this.ecosystemGrid[i][j];

        // 更新环境参数
        cell.temperature = this.calculateTemperature(
          cell.position.x,
          cell.position.z,
          cell.position.y,
          this.currentSeason
        );

        // 更新植被数据
        if (this.useVegetationGrowth) {
          this.updateVegetationGrowth(cell, deltaTime);
        }

        // 更新植被竞争
        if (this.useVegetationCompetition) {
          this.updateVegetationCompetition(cell, deltaTime);
        }
      }
    }

    // 更新植被分布
    this.updateVegetationDistribution();
  }

  /**
   * 更新季节
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateSeason(deltaTime: number): void {
    // 更新季节计时器
    this.seasonChangeTimer += deltaTime;
    if (this.seasonChangeTimer >= this.seasonDuration) {
      this.seasonChangeTimer = 0;

      // 切换到下一个季节
      switch (this.currentSeason) {
        case SeasonType.SPRING:
          this.currentSeason = SeasonType.SUMMER;
          break;
        case SeasonType.SUMMER:
          this.currentSeason = SeasonType.AUTUMN;
          break;
        case SeasonType.AUTUMN:
          this.currentSeason = SeasonType.WINTER;
          break;
        case SeasonType.WINTER:
          this.currentSeason = SeasonType.SPRING;
          break;
      }

      // 发出季节变化事件
      this.eventEmitter.emit(EcosystemSimulationSystemEventType.SEASON_CHANGED, this.currentSeason);

      Debug.log('EcosystemSimulationSystem', `季节变化: ${this.currentSeason}`);
    }
  }

  /**
   * 更新植被生长
   * @param cell 生态系统网格单元
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateVegetationGrowth(cell: EcosystemGridCell, deltaTime: number): void {
    // 遍历所有植被数据
    for (let i = 0; i < cell.vegetationData.length; i++) {
      const vegetation = cell.vegetationData[i];

      // 跳过死亡的植被
      if (vegetation.growthStage === VegetationGrowthStage.DEAD) {
        continue;
      }

      // 更新年龄
      vegetation.age += deltaTime / (60 * 60 * 24); // 转换为天数

      // 更新健康度
      vegetation.health = this.calculateVegetationHealth(vegetation, cell);

      // 如果健康度过低，植被死亡
      if (vegetation.health <= 0) {
        vegetation.growthStage = VegetationGrowthStage.DEAD;
        continue;
      }

      // 如果年龄超过最大寿命，植被死亡
      if (vegetation.age >= vegetation.maxAge) {
        vegetation.growthStage = VegetationGrowthStage.DEAD;
        continue;
      }

      // 更新生长阶段
      this.updateGrowthStage(vegetation);

      // 更新高度和宽度
      this.updateVegetationSize(vegetation, deltaTime);

      // 更新季节性特征
      this.updateSeasonalFeatures(vegetation, this.currentSeason);
    }

    // 移除死亡的植被
    cell.vegetationData = cell.vegetationData.filter(v => v.growthStage !== VegetationGrowthStage.DEAD);
  }

  /**
   * 更新植被竞争
   * @param cell 生态系统网格单元
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateVegetationCompetition(cell: EcosystemGridCell, deltaTime: number): void {
    // 如果植被数量小于2，不需要竞争
    if (cell.vegetationData.length < 2) {
      return;
    }

    // 计算总竞争压力
    for (let i = 0; i < cell.vegetationData.length; i++) {
      const vegetation = cell.vegetationData[i];
      let competitionPressure = 0;

      // 计算来自其他植被的竞争压力
      for (let j = 0; j < cell.vegetationData.length; j++) {
        if (i === j) continue;

        const otherVegetation = cell.vegetationData[j];
        const competitionFactor = this.calculateCompetitionFactor(vegetation, otherVegetation);
        competitionPressure += competitionFactor;
      }

      // 更新健康度
      vegetation.health -= competitionPressure * deltaTime * 0.01;
    }
  }

  /**
   * 更新植被分布
   */
  private updateVegetationDistribution(): void {
    // 遍历所有植被组件
    for (const [entity, component] of this.vegetationComponents.entries()) {
      // 如果组件未启用，则跳过
      if (!component.initialized) {
        continue;
      }

      // 更新植被分布
      this.updateVegetationComponentDistribution(entity, component);
    }

    // 发出植被分布更新事件
    this.eventEmitter.emit(EcosystemSimulationSystemEventType.VEGETATION_DISTRIBUTION_UPDATED);
  }

  /**
   * 更新植被组件分布
   * @param entity 实体
   * @param component 植被组件
   */
  private updateVegetationComponentDistribution(_entity: Entity, component: VegetationComponent): void {
    // 如果没有生态系统网格，则跳过
    if (!this.ecosystemGrid) {
      return;
    }

    // 获取地形组件
    const terrainEntity = component.terrainEntity;
    let terrainComponent: TerrainComponent | null = null;
    for (const [entity, component] of this.terrainComponents.entries()) {
      if (entity.id === terrainEntity) {
        terrainComponent = component;
        break;
      }
    }

    if (!terrainComponent) {
      return;
    }

    // 更新植被分布
    // 这里可以根据生态系统网格中的植被数据更新植被组件的分布
    // 例如，可以根据植被类型、生长阶段、健康度等调整植被密度、大小等
  }

  /**
   * 更新生长阶段
   * @param vegetation 植被数据
   */
  private updateGrowthStage(vegetation: VegetationData): void {
    // 根据年龄和最大寿命计算生长阶段
    const ageRatio = vegetation.age / vegetation.maxAge;

    if (ageRatio < 0.1) {
      vegetation.growthStage = VegetationGrowthStage.SEED;
    } else if (ageRatio < 0.2) {
      vegetation.growthStage = VegetationGrowthStage.SEEDLING;
    } else if (ageRatio < 0.7) {
      vegetation.growthStage = VegetationGrowthStage.JUVENILE;
    } else if (ageRatio < 0.9) {
      vegetation.growthStage = VegetationGrowthStage.ADULT;
    } else {
      vegetation.growthStage = VegetationGrowthStage.SENIOR;
    }
  }

  /**
   * 更新植被大小
   * @param vegetation 植被数据
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateVegetationSize(vegetation: VegetationData, deltaTime: number): void {
    // 根据生长阶段和生长速度计算大小
    const growthFactor = deltaTime * vegetation.growthRate * 0.01;

    switch (vegetation.growthStage) {
      case VegetationGrowthStage.SEED:
        vegetation.currentHeight = vegetation.maxHeight * 0.05;
        vegetation.currentWidth = vegetation.maxWidth * 0.05;
        break;
      case VegetationGrowthStage.SEEDLING:
        vegetation.currentHeight = Math.min(vegetation.currentHeight + growthFactor, vegetation.maxHeight * 0.2);
        vegetation.currentWidth = Math.min(vegetation.currentWidth + growthFactor, vegetation.maxWidth * 0.2);
        break;
      case VegetationGrowthStage.JUVENILE:
        vegetation.currentHeight = Math.min(vegetation.currentHeight + growthFactor, vegetation.maxHeight * 0.7);
        vegetation.currentWidth = Math.min(vegetation.currentWidth + growthFactor, vegetation.maxWidth * 0.7);
        break;
      case VegetationGrowthStage.ADULT:
        vegetation.currentHeight = Math.min(vegetation.currentHeight + growthFactor * 0.5, vegetation.maxHeight);
        vegetation.currentWidth = Math.min(vegetation.currentWidth + growthFactor * 0.5, vegetation.maxWidth);
        break;
      case VegetationGrowthStage.SENIOR:
        // 老年植物不再生长
        break;
      case VegetationGrowthStage.DEAD:
        // 死亡植物不再生长
        break;
    }
  }

  /**
   * 更新季节性特征
   * @param vegetation 植被数据
   * @param season 季节
   */
  private updateSeasonalFeatures(vegetation: VegetationData, season: SeasonType): void {
    // 更新开花状态
    vegetation.flowering = vegetation.floweringSeason.includes(season);

    // 更新结果状态
    vegetation.fruiting = vegetation.fruitingSeason.includes(season);

    // 更新落叶状态
    if (vegetation.deciduous) {
      // 如果是落叶植物，检查是否在落叶季节
      vegetation.deciduous = vegetation.leafFallSeason.includes(season);
    }
  }

  /**
   * 计算植被健康度
   * @param vegetation 植被数据
   * @param cell 生态系统网格单元
   * @returns 健康度
   */
  private calculateVegetationHealth(vegetation: VegetationData, cell: EcosystemGridCell): number {
    // 基础健康度
    let health = vegetation.health;

    // 环境因素影响
    const moistureFactor = this.calculateMoistureFactor(vegetation, cell.soilMoisture);
    const temperatureFactor = this.calculateTemperatureFactor(vegetation, cell.temperature);
    const lightFactor = this.calculateLightFactor(vegetation, cell.lightIntensity);
    const nutrientFactor = this.calculateNutrientFactor(vegetation, cell.soilFertility);

    // 综合环境因素
    const environmentFactor = (moistureFactor + temperatureFactor + lightFactor + nutrientFactor) / 4;

    // 更新健康度
    health = Math.max(0, Math.min(1, health + environmentFactor * 0.01));

    return health;
  }

  /**
   * 计算水分因子
   * @param vegetation 植被数据
   * @param moisture 土壤湿度
   * @returns 水分因子
   */
  private calculateMoistureFactor(vegetation: VegetationData, moisture: number): number {
    // 计算水分适应度
    const adaptability = vegetation.wetTolerance;
    const optimalMoisture = adaptability * 0.5 + 0.25;
    const moistureDifference = Math.abs(moisture - optimalMoisture);

    // 水分因子，越接近最佳湿度，因子越高
    return Math.max(0, 1 - moistureDifference * 2);
  }

  /**
   * 计算温度因子
   * @param vegetation 植被数据
   * @param temperature 温度
   * @returns 温度因子
   */
  private calculateTemperatureFactor(vegetation: VegetationData, temperature: number): number {
    // 计算温度适应度
    const coldTolerance = vegetation.coldTolerance;
    const heatTolerance = vegetation.heatTolerance;
    const optimalTemperature = (coldTolerance + heatTolerance) / 2;
    const temperatureRange = heatTolerance - coldTolerance;
    const temperatureDifference = Math.abs(temperature - optimalTemperature);

    // 温度因子，越接近最佳温度，因子越高
    return Math.max(0, 1 - (temperatureDifference * 2) / temperatureRange);
  }

  /**
   * 计算光照因子
   * @param vegetation 植被数据
   * @param lightIntensity 光照强度
   * @returns 光照因子
   */
  private calculateLightFactor(vegetation: VegetationData, lightIntensity: number): number {
    // 计算光照适应度
    const lightRequirement = vegetation.lightRequirement;
    const optimalLight = lightRequirement;
    const lightDifference = Math.abs(lightIntensity - optimalLight);

    // 光照因子，越接近最佳光照，因子越高
    return Math.max(0, 1 - lightDifference * 2);
  }

  /**
   * 计算营养因子
   * @param vegetation 植被数据
   * @param fertility 土壤肥力
   * @returns 营养因子
   */
  private calculateNutrientFactor(vegetation: VegetationData, fertility: number): number {
    // 计算营养适应度
    const nutrientRequirement = vegetation.nutrientRequirement;
    const optimalNutrient = nutrientRequirement;
    const nutrientDifference = Math.abs(fertility - optimalNutrient);

    // 营养因子，越接近最佳营养，因子越高
    return Math.max(0, 1 - nutrientDifference * 2);
  }

  /**
   * 计算竞争因子
   * @param vegetation 植被数据
   * @param otherVegetation 其他植被数据
   * @returns 竞争因子
   */
  private calculateCompetitionFactor(vegetation: VegetationData, otherVegetation: VegetationData): number {
    // 计算竞争因子
    const heightDifference = otherVegetation.currentHeight - vegetation.currentHeight;
    const widthDifference = otherVegetation.currentWidth - vegetation.currentWidth;
    const competitivenessDifference = otherVegetation.competitiveness - vegetation.competitiveness;

    // 综合竞争因子
    let competitionFactor = 0;

    // 高度竞争（较高的植物会遮挡光照）
    if (heightDifference > 0) {
      competitionFactor += heightDifference / vegetation.maxHeight * 0.5;
    }

    // 宽度竞争（较宽的植物会占用更多空间）
    if (widthDifference > 0) {
      competitionFactor += widthDifference / vegetation.maxWidth * 0.3;
    }

    // 竞争力竞争（竞争力更强的植物会更有优势）
    if (competitivenessDifference > 0) {
      competitionFactor += competitivenessDifference * 0.2;
    }

    return competitionFactor;
  }

  /**
   * 计算土壤湿度
   * @param x X坐标
   * @param z Z坐标
   * @param y 高度
   * @param terrainComponent 地形组件
   * @returns 土壤湿度
   */
  private calculateSoilMoisture(x: number, z: number, y: number, terrainComponent: TerrainComponent): number {
    // 简单的土壤湿度计算
    // 高度越低，湿度越高
    const heightFactor = Math.max(0, 1 - (y / 100));

    // 水域附近湿度更高
    const waterProximity = this.calculateWaterProximity(x, z, terrainComponent);

    // 综合湿度
    return Math.min(1, heightFactor * 0.7 + waterProximity * 0.3);
  }

  /**
   * 计算水域接近度
   * @param x X坐标
   * @param z Z坐标
   * @param terrainComponent 地形组件
   * @returns 水域接近度
   */
  private calculateWaterProximity(x: number, z: number, terrainComponent: TerrainComponent): number {
    // 简单的水域接近度计算
    // 检查周围点是否有水域
    const checkRadius = 10;
    const checkStep = 2;
    let waterCount = 0;
    let totalChecks = 0;

    for (let dx = -checkRadius; dx <= checkRadius; dx += checkStep) {
      for (let dz = -checkRadius; dz <= checkRadius; dz += checkStep) {
        const checkX = x + dx;
        const checkZ = z + dz;
        const height = terrainComponent.getHeight(checkX, checkZ);

        if (height < 0) {
          // 水域
          const distance = Math.sqrt(dx * dx + dz * dz);
          waterCount += Math.max(0, 1 - (distance / checkRadius));
        }

        totalChecks++;
      }
    }

    return totalChecks > 0 ? waterCount / totalChecks : 0;
  }

  /**
   * 计算土壤肥力
   * @param x X坐标
   * @param z Z坐标
   * @param y 高度
   * @param terrainComponent 地形组件
   * @returns 土壤肥力
   */
  private calculateSoilFertility(x: number, z: number, _y: number, terrainComponent: TerrainComponent): number {
    // 简单的土壤肥力计算
    // 使用柏林噪声生成肥力分布
    const fertility = this.perlinNoise(x * 0.01, z * 0.01, 0) * 0.5 + 0.5;

    // 水域附近肥力更高
    const waterProximity = this.calculateWaterProximity(x, z, terrainComponent);

    // 综合肥力
    return Math.min(1, fertility * 0.7 + waterProximity * 0.3);
  }

  /**
   * 计算光照强度
   * @param x X坐标
   * @param z Z坐标
   * @param y 高度
   * @param normal 法线
   * @returns 光照强度
   */
  private calculateLightIntensity(_x: number, _z: number, y: number, normal: THREE.Vector3): number {
    // 简单的光照强度计算
    // 高度越高，光照越强
    const heightFactor = Math.min(1, y / 100);

    // 坡向影响光照
    const slopeFactor = Math.max(0, normal.dot(new THREE.Vector3(0, 1, 0)));

    // 综合光照强度
    return Math.min(1, heightFactor * 0.3 + slopeFactor * 0.7);
  }

  /**
   * 计算温度
   * @param x X坐标
   * @param z Z坐标
   * @param y 高度
   * @param season 季节
   * @returns 温度
   */
  private calculateTemperature(_x: number, _z: number, y: number, season: SeasonType): number {
    // 简单的温度计算
    // 高度越高，温度越低
    const heightFactor = Math.max(0, 1 - (y / 200));

    // 季节影响温度
    let seasonFactor = 0.5;
    switch (season) {
      case SeasonType.SPRING:
        seasonFactor = 0.6;
        break;
      case SeasonType.SUMMER:
        seasonFactor = 0.8;
        break;
      case SeasonType.AUTUMN:
        seasonFactor = 0.4;
        break;
      case SeasonType.WINTER:
        seasonFactor = 0.2;
        break;
    }

    // 综合温度
    return heightFactor * seasonFactor;
  }

  /**
   * 获取地形法线
   * @param terrainComponent 地形组件
   * @param x X坐标
   * @param z Z坐标
   * @returns 法线
   */
  private getTerrainNormal(terrainComponent: TerrainComponent, x: number, z: number): THREE.Vector3 {
    // 将世界坐标转换为地形坐标
    const terrainX = ((x + terrainComponent.width / 2) / terrainComponent.width) * (terrainComponent.resolution - 1);
    const terrainZ = ((z + terrainComponent.height / 2) / terrainComponent.height) * (terrainComponent.resolution - 1);

    // 获取整数坐标
    const x0 = Math.floor(terrainX);
    const z0 = Math.floor(terrainZ);

    // 确保坐标在有效范围内
    if (x0 < 0 || x0 >= terrainComponent.resolution - 1 || z0 < 0 || z0 >= terrainComponent.resolution - 1) {
      return new THREE.Vector3(0, 1, 0);
    }

    // 计算法线索引
    const index = (z0 * terrainComponent.resolution + x0) * 3;

    // 获取法线
    return new THREE.Vector3(
      terrainComponent.normalData[index],
      terrainComponent.normalData[index + 1],
      terrainComponent.normalData[index + 2]
    );
  }

  /**
   * 计算坡度
   * @param normal 法线
   * @returns 坡度（度）
   */
  private calculateSlope(normal: THREE.Vector3): number {
    // 计算法线与上方向的夹角
    const angle = Math.acos(normal.dot(new THREE.Vector3(0, 1, 0)));
    // 转换为度
    return angle * (180 / Math.PI);
  }

  /**
   * 计算坡向
   * @param normal 法线
   * @returns 坡向（度）
   */
  private calculateAspect(normal: THREE.Vector3): number {
    // 计算坡向（方位角）
    const aspect = Math.atan2(normal.x, normal.z);
    // 转换为度
    return ((aspect * (180 / Math.PI)) + 360) % 360;
  }

  /**
   * 柏林噪声
   * @param x X坐标
   * @param y Y坐标
   * @param z Z坐标
   * @returns 噪声值
   */
  private perlinNoise(x: number, y: number, z: number): number {
    // 简单的柏林噪声实现
    const X = Math.floor(x) & 255;
    const Y = Math.floor(y) & 255;
    const Z = Math.floor(z) & 255;

    x -= Math.floor(x);
    y -= Math.floor(y);
    z -= Math.floor(z);

    const u = this.fade(x);
    const v = this.fade(y);
    const w = this.fade(z);

    const A = this.p[X] + Y;
    const AA = this.p[A] + Z;
    const AB = this.p[A + 1] + Z;
    const B = this.p[X + 1] + Y;
    const BA = this.p[B] + Z;
    const BB = this.p[B + 1] + Z;

    return this.lerp(w, this.lerp(v, this.lerp(u, this.grad(this.p[AA], x, y, z),
      this.grad(this.p[BA], x - 1, y, z)),
      this.lerp(u, this.grad(this.p[AB], x, y - 1, z),
        this.grad(this.p[BB], x - 1, y - 1, z))),
      this.lerp(v, this.lerp(u, this.grad(this.p[AA + 1], x, y, z - 1),
        this.grad(this.p[BA + 1], x - 1, y, z - 1)),
        this.lerp(u, this.grad(this.p[AB + 1], x, y - 1, z - 1),
          this.grad(this.p[BB + 1], x - 1, y - 1, z - 1))));
  }

  /**
   * 柏林噪声辅助函数：fade
   * @param t 参数
   * @returns 结果
   */
  private fade(t: number): number {
    return t * t * t * (t * (t * 6 - 15) + 10);
  }

  /**
   * 柏林噪声辅助函数：lerp
   * @param t 参数
   * @param a 参数
   * @param b 参数
   * @returns 结果
   */
  private lerp(t: number, a: number, b: number): number {
    return a + t * (b - a);
  }

  /**
   * 柏林噪声辅助函数：grad
   * @param hash 参数
   * @param x 参数
   * @param y 参数
   * @param z 参数
   * @returns 结果
   */
  private grad(hash: number, x: number, y: number, z: number): number {
    const h = hash & 15;
    const u = h < 8 ? x : y;
    const v = h < 4 ? y : h === 12 || h === 14 ? x : z;
    return ((h & 1) === 0 ? u : -u) + ((h & 2) === 0 ? v : -v);
  }

  /**
   * 柏林噪声辅助数组
   */
  private p = new Array(512);

  /**
   * 初始化柏林噪声
   */
  private initPerlinNoise(): void {
    const permutation = [151, 160, 137, 91, 90, 15, 131, 13, 201, 95, 96, 53, 194, 233, 7, 225, 140, 36, 103, 30, 69, 142, 8, 99, 37, 240, 21, 10, 23, 190, 6, 148, 247, 120, 234, 75, 0, 26, 197, 62, 94, 252, 219, 203, 117, 35, 11, 32, 57, 177, 33, 88, 237, 149, 56, 87, 174, 20, 125, 136, 171, 168, 68, 175, 74, 165, 71, 134, 139, 48, 27, 166, 77, 146, 158, 231, 83, 111, 229, 122, 60, 211, 133, 230, 220, 105, 92, 41, 55, 46, 245, 40, 244, 102, 143, 54, 65, 25, 63, 161, 1, 216, 80, 73, 209, 76, 132, 187, 208, 89, 18, 169, 200, 196, 135, 130, 116, 188, 159, 86, 164, 100, 109, 198, 173, 186, 3, 64, 52, 217, 226, 250, 124, 123, 5, 202, 38, 147, 118, 126, 255, 82, 85, 212, 207, 206, 59, 227, 47, 16, 58, 17, 182, 189, 28, 42, 223, 183, 170, 213, 119, 248, 152, 2, 44, 154, 163, 70, 221, 153, 101, 155, 167, 43, 172, 9, 129, 22, 39, 253, 19, 98, 108, 110, 79, 113, 224, 232, 178, 185, 112, 104, 218, 246, 97, 228, 251, 34, 242, 193, 238, 210, 144, 12, 191, 179, 162, 241, 81, 51, 145, 235, 249, 14, 239, 107, 49, 192, 214, 31, 181, 199, 106, 157, 184, 84, 204, 176, 115, 121, 50, 45, 127, 4, 150, 254, 138, 236, 205, 93, 222, 114, 67, 29, 24, 72, 243, 141, 128, 195, 78, 66, 215, 61, 156, 180];

    for (let i = 0; i < 256; i++) {
      this.p[i] = permutation[i];
      this.p[256 + i] = permutation[i];
    }
  }

  /**
   * 初始化调试可视化
   */
  private initDebugVisualization(): void {
    // 清除现有调试网格
    this.clearDebugMeshes();

    // 如果没有生态系统网格，则返回
    if (!this.ecosystemGrid) {
      return;
    }

    // 获取活跃场景
    const scene = this.world.getActiveScene();
    if (!scene) {
      return;
    }

    // 创建网格可视化
    const resolution = this.ecosystemGridResolution;
    for (let i = 0; i < resolution; i++) {
      for (let j = 0; j < resolution; j++) {
        const cell = this.ecosystemGrid[i][j];

        // 创建网格单元可视化
        const geometry = new THREE.BoxGeometry(cell.size.x, 1, cell.size.z);
        const material = new THREE.MeshBasicMaterial({
          color: this.getCellColor(cell),
          transparent: true,
          opacity: 0.5,
          wireframe: true
        });
        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.copy(cell.position);
        mesh.position.y += 0.5; // 将网格单元放在地面上方

        // 添加到场景
        scene.getThreeScene().add(mesh);
        this.debugMeshes.push(mesh);
      }
    }
  }

  /**
   * 更新调试可视化
   */
  private updateDebugVisualization(): void {
    // 如果没有生态系统网格，则返回
    if (!this.ecosystemGrid) {
      return;
    }

    // 获取活跃场景
    const scene = this.world.getActiveScene();
    if (!scene) {
      return;
    }

    // 更新网格可视化
    const resolution = this.ecosystemGridResolution;
    let meshIndex = 0;
    for (let i = 0; i < resolution; i++) {
      for (let j = 0; j < resolution; j++) {
        const cell = this.ecosystemGrid[i][j];

        // 更新网格单元可视化
        if (meshIndex < this.debugMeshes.length) {
          const mesh = this.debugMeshes[meshIndex++];
          (mesh.material as THREE.MeshBasicMaterial).color.set(this.getCellColor(cell));
        }
      }
    }
  }

  /**
   * 获取网格单元颜色
   * @param cell 生态系统网格单元
   * @returns 颜色
   */
  private getCellColor(cell: EcosystemGridCell): number {
    // 根据网格单元属性计算颜色
    if (cell.isWater) {
      return 0x0000ff; // 蓝色表示水域
    }

    // 根据土壤湿度计算颜色
    const r = Math.floor((1 - cell.soilMoisture) * 255);
    const g = Math.floor(cell.soilFertility * 255);
    const b = Math.floor(cell.soilMoisture * 255);

    return (r << 16) | (g << 8) | b;
  }

  /**
   * 清除调试网格
   */
  private clearDebugMeshes(): void {
    // 获取活跃场景
    const scene = this.world.getActiveScene();
    if (!scene) {
      return;
    }

    // 移除所有调试网格
    for (const mesh of this.debugMeshes) {
      scene.getThreeScene().remove(mesh);
    }

    // 清空调试网格列表
    this.debugMeshes = [];
  }
}