/**
 * 植被模块
 * 导出所有植被相关的类和接口
 */

// 导出植被组件（类和接口分开导出）
export { VegetationComponent } from './components/VegetationComponent';
export type {
  VegetationComponentOptions,
  VegetationItemConfig,
  VegetationInstance
} from './components/VegetationComponent';

// 导出植被系统（类和接口分开导出）
export { VegetationSystem, VegetationSystemEventType } from './VegetationSystem';
export type { VegetationSystemOptions } from './VegetationSystem';
