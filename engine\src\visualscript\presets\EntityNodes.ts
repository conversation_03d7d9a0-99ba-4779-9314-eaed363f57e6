/**
 * 视觉脚本实体节点
 * 提供实体操作相关的节点
 */
import { Entity, getComponent, hasComponent, addComponent, removeComponent } from '@dl-engine/ecs';
import { FlowNode } from '../nodes/FlowNode';
import { NodeCategory, NodeType, SocketDirection, SocketType, NodeOptions } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';

/**
 * 组件类注册表
 * 用于根据组件类型名称获取组件类
 */
interface ComponentRegistry {
  [key: string]: any;
}

// 全局组件注册表
let componentRegistry: ComponentRegistry = {};

/**
 * 注册组件类
 * @param name 组件类型名称
 * @param componentClass 组件类
 */
export function registerComponent(name: string, componentClass: any): void {
  componentRegistry[name] = componentClass;
}

/**
 * 获取组件类
 * @param name 组件类型名称
 * @returns 组件类
 */
export function getComponentClass(name: string): any {
  return componentRegistry[name] || null;
}

/**
 * 实体管理器接口
 */
interface EntityManager {
  getEntity(id: number): Entity | null;
  createEntity(): Entity;
  destroyEntity(entity: Entity): boolean;
}

// 全局实体管理器
let entityManager: EntityManager | null = null;

/**
 * 设置实体管理器
 * @param manager 实体管理器
 */
export function setEntityManager(manager: EntityManager): void {
  entityManager = manager;
}

/**
 * 获取实体节点
 * 根据ID获取实体
 */
export class GetEntityNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加实体ID输入
    this.addInput({
      name: 'entityId',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '实体ID',
      defaultValue: 0
    });

    // 添加实体输出
    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'entity',
      description: '实体'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加失败输出流程插槽
    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败时执行'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entityId = this.getInputValue('entityId') as number;

    // 检查实体管理器是否可用
    if (!entityManager) {
      console.error('实体管理器未设置');
      this.triggerFlow('fail');
      return null;
    }

    // 获取实体
    const entity = entityManager.getEntity(entityId);

    // 检查实体是否存在
    if (entity) {
      // 设置输出值
      this.setOutputValue('entity', entity);

      // 触发输出流程
      this.triggerFlow('flow');

      return entity;
    } else {
      // 触发失败流程
      this.triggerFlow('fail');

      return null;
    }
  }
}

/**
 * 获取组件节点
 * 获取实体上的组件
 */
export class GetComponentNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '实体'
    });

    // 添加组件类型输入
    this.addInput({
      name: 'componentType',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '组件类型',
      defaultValue: ''
    });

    // 添加组件输出
    this.addOutput({
      name: 'component',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'component',
      description: '组件'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加失败输出流程插槽
    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败时执行'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const componentType = this.getInputValue('componentType') as string;

    // 检查实体和组件类型是否有效
    if (!entity || !componentType) {
      // 触发失败流程
      this.triggerFlow('fail');
      return null;
    }

    // 获取组件类
    const ComponentClass = getComponentClass(componentType);

    // 检查组件类是否存在
    if (!ComponentClass) {
      // 触发失败流程
      this.triggerFlow('fail');
      return null;
    }

    // 检查实体是否有该组件
    if (hasComponent(entity, ComponentClass)) {
      // 获取组件
      const component = getComponent(entity, ComponentClass);

      // 设置输出值
      this.setOutputValue('component', component);

      // 触发输出流程
      this.triggerFlow('flow');

      return component;
    } else {
      // 触发失败流程
      this.triggerFlow('fail');

      return null;
    }
  }


}

/**
 * 添加组件节点
 * 向实体添加组件
 */
export class AddComponentNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '实体'
    });

    // 添加组件类型输入
    this.addInput({
      name: 'componentType',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '组件类型',
      defaultValue: ''
    });

    // 添加组件数据输入
    this.addInput({
      name: 'data',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '组件数据',
      defaultValue: {}
    });

    // 添加组件输出
    this.addOutput({
      name: 'component',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'component',
      description: '组件'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加失败输出流程插槽
    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败时执行'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const componentType = this.getInputValue('componentType') as string;
    const data = this.getInputValue('data') as object;

    // 检查实体和组件类型是否有效
    if (!entity || !componentType) {
      // 触发失败流程
      this.triggerFlow('fail');
      return null;
    }

    // 获取组件类
    const ComponentClass = getComponentClass(componentType);

    // 检查组件类是否存在
    if (!ComponentClass) {
      // 触发失败流程
      this.triggerFlow('fail');
      return null;
    }

    try {
      // 添加组件
      const component = addComponent(entity, ComponentClass, data);

      // 设置输出值
      this.setOutputValue('component', component);

      // 触发输出流程
      this.triggerFlow('flow');

      return component;
    } catch (error) {
      console.error('添加组件失败:', error);

      // 触发失败流程
      this.triggerFlow('fail');

      return null;
    }
  }
}

/**
 * 移除组件节点
 * 从实体移除组件
 */
export class RemoveComponentNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '实体'
    });

    // 添加组件类型输入
    this.addInput({
      name: 'componentType',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '组件类型',
      defaultValue: ''
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加失败输出流程插槽
    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败时执行'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const componentType = this.getInputValue('componentType') as string;

    // 检查实体和组件类型是否有效
    if (!entity || !componentType) {
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }

    // 获取组件类
    const ComponentClass = getComponentClass(componentType);

    // 检查组件类是否存在
    if (!ComponentClass) {
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 检查实体是否有该组件
      if (hasComponent(entity, ComponentClass)) {
        // 移除组件
        removeComponent(entity, ComponentClass);

        // 触发输出流程
        this.triggerFlow('flow');

        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');

        return false;
      }
    } catch (error) {
      console.error('移除组件失败:', error);

      // 触发失败流程
      this.triggerFlow('fail');

      return false;
    }
  }
}

/**
 * 检查组件节点
 * 检查实体是否有指定组件
 */
export class HasComponentNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加实体输入
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'entity',
      description: '实体'
    });

    // 添加组件类型输入
    this.addInput({
      name: 'componentType',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '组件类型',
      defaultValue: ''
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '检查结果'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加失败输出流程插槽
    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败时执行'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const componentType = this.getInputValue('componentType') as string;

    // 检查实体和组件类型是否有效
    if (!entity || !componentType) {
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }

    // 获取组件类
    const ComponentClass = getComponentClass(componentType);

    // 检查组件类是否存在
    if (!ComponentClass) {
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }

    // 检查实体是否有该组件
    const result = hasComponent(entity, ComponentClass);

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 注册实体节点
 * @param registry 节点注册表
 */
export function registerEntityNodes(registry: NodeRegistry): void {
  // 注册获取实体节点
  registry.registerNodeType({
    type: 'entity/get',
    category: NodeCategory.ENTITY,
    constructor: GetEntityNode,
    label: '获取实体',
    description: '根据ID获取实体',
    icon: 'entity',
    color: '#4CAF50',
    tags: ['entity', 'get']
  });

  // 注册获取组件节点
  registry.registerNodeType({
    type: 'entity/component/get',
    category: NodeCategory.ENTITY,
    constructor: GetComponentNode,
    label: '获取组件',
    description: '获取实体上的组件',
    icon: 'component',
    color: '#4CAF50',
    tags: ['entity', 'component', 'get']
  });

  // 注册添加组件节点
  registry.registerNodeType({
    type: 'entity/component/add',
    category: NodeCategory.ENTITY,
    constructor: AddComponentNode,
    label: '添加组件',
    description: '向实体添加组件',
    icon: 'addComponent',
    color: '#4CAF50',
    tags: ['entity', 'component', 'add']
  });

  // 注册移除组件节点
  registry.registerNodeType({
    type: 'entity/component/remove',
    category: NodeCategory.ENTITY,
    constructor: RemoveComponentNode,
    label: '移除组件',
    description: '从实体移除组件',
    icon: 'removeComponent',
    color: '#4CAF50',
    tags: ['entity', 'component', 'remove']
  });

  // 注册检查组件节点
  registry.registerNodeType({
    type: 'entity/component/has',
    category: NodeCategory.ENTITY,
    constructor: HasComponentNode,
    label: '检查组件',
    description: '检查实体是否有指定组件',
    icon: 'hasComponent',
    color: '#4CAF50',
    tags: ['entity', 'component', 'has']
  });
}
