/**
 * 视觉脚本图形
 * 表示一个完整的视觉脚本图形，包含节点、变量和自定义事件
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { CustomEvent } from '../events/CustomEvent';
import { Node } from '../nodes/Node';
import { Variable } from '../values/Variable';
import { GraphJSON } from './GraphJSON';

/**
 * 图形选项
 */
export interface GraphOptions {
  /** 图形ID */
  id: string;
  /** 图形名称 */
  name?: string;
  /** 图形描述 */
  description?: string;
}

/**
 * 图形类
 */
export class Graph extends EventEmitter {
  /** 图形ID */
  public readonly id: string;
  
  /** 图形名称 */
  private name: string;
  
  /** 图形描述 */
  private description: string;
  
  /** 节点映射 */
  private nodes: Map<string, Node> = new Map();
  
  /** 变量映射 */
  private variables: Map<string, Variable> = new Map();
  
  /** 自定义事件映射 */
  private customEvents: Map<string, CustomEvent> = new Map();
  
  /** 创建时间 */
  private createdAt: Date;
  
  /** 最后修改时间 */
  private updatedAt: Date;
  
  /**
   * 创建图形
   * @param options 图形选项
   */
  constructor(options: GraphOptions) {
    super();
    
    this.id = options.id;
    this.name = options.name || '未命名图形';
    this.description = options.description || '';
    this.createdAt = new Date();
    this.updatedAt = new Date();
  }
  
  /**
   * 获取图形名称
   * @returns 图形名称
   */
  public getName(): string {
    return this.name;
  }
  
  /**
   * 设置图形名称
   * @param name 图形名称
   */
  public setName(name: string): void {
    this.name = name;
    this.updatedAt = new Date();
    this.emit('nameChanged', name);
  }
  
  /**
   * 获取图形描述
   * @returns 图形描述
   */
  public getDescription(): string {
    return this.description;
  }
  
  /**
   * 设置图形描述
   * @param description 图形描述
   */
  public setDescription(description: string): void {
    this.description = description;
    this.updatedAt = new Date();
    this.emit('descriptionChanged', description);
  }
  
  /**
   * 添加节点
   * @param node 节点
   * @returns 是否添加成功
   */
  public addNode(node: Node): boolean {
    // 检查是否已存在
    if (this.nodes.has(node.id)) {
      return false;
    }
    
    // 添加节点
    this.nodes.set(node.id, node);
    
    // 更新修改时间
    this.updatedAt = new Date();
    
    // 触发添加事件
    this.emit('nodeAdded', node);
    
    return true;
  }
  
  /**
   * 移除节点
   * @param id 节点ID
   * @returns 是否移除成功
   */
  public removeNode(id: string): boolean {
    // 检查是否存在
    const node = this.nodes.get(id);
    
    if (!node) {
      return false;
    }
    
    // 断开节点连接
    node.disconnectAll();
    
    // 移除节点
    this.nodes.delete(id);
    
    // 更新修改时间
    this.updatedAt = new Date();
    
    // 触发移除事件
    this.emit('nodeRemoved', node);
    
    return true;
  }
  
  /**
   * 获取节点
   * @param id 节点ID
   * @returns 节点
   */
  public getNode(id: string): Node | undefined {
    return this.nodes.get(id);
  }
  
  /**
   * 获取所有节点
   * @returns 节点列表
   */
  public getNodes(): Node[] {
    return Array.from(this.nodes.values());
  }
  
  /**
   * 添加变量
   * @param variable 变量
   * @returns 是否添加成功
   */
  public addVariable(variable: Variable): boolean {
    // 检查是否已存在
    if (this.variables.has(variable.id)) {
      return false;
    }
    
    // 添加变量
    this.variables.set(variable.id, variable);
    
    // 更新修改时间
    this.updatedAt = new Date();
    
    // 触发添加事件
    this.emit('variableAdded', variable);
    
    return true;
  }
  
  /**
   * 移除变量
   * @param id 变量ID
   * @returns 是否移除成功
   */
  public removeVariable(id: string): boolean {
    // 检查是否存在
    const variable = this.variables.get(id);
    
    if (!variable) {
      return false;
    }
    
    // 移除变量
    this.variables.delete(id);
    
    // 更新修改时间
    this.updatedAt = new Date();
    
    // 触发移除事件
    this.emit('variableRemoved', variable);
    
    return true;
  }
  
  /**
   * 获取变量
   * @param id 变量ID
   * @returns 变量
   */
  public getVariable(id: string): Variable | undefined {
    return this.variables.get(id);
  }
  
  /**
   * 获取所有变量
   * @returns 变量列表
   */
  public getVariables(): Variable[] {
    return Array.from(this.variables.values());
  }
  
  /**
   * 添加自定义事件
   * @param event 自定义事件
   * @returns 是否添加成功
   */
  public addCustomEvent(event: CustomEvent): boolean {
    // 检查是否已存在
    if (this.customEvents.has(event.id)) {
      return false;
    }
    
    // 添加自定义事件
    this.customEvents.set(event.id, event);
    
    // 更新修改时间
    this.updatedAt = new Date();
    
    // 触发添加事件
    this.emit('customEventAdded', event);
    
    return true;
  }
  
  /**
   * 移除自定义事件
   * @param id 自定义事件ID
   * @returns 是否移除成功
   */
  public removeCustomEvent(id: string): boolean {
    // 检查是否存在
    const event = this.customEvents.get(id);
    
    if (!event) {
      return false;
    }
    
    // 移除自定义事件
    this.customEvents.delete(id);
    
    // 更新修改时间
    this.updatedAt = new Date();
    
    // 触发移除事件
    this.emit('customEventRemoved', event);
    
    return true;
  }
  
  /**
   * 获取自定义事件
   * @param id 自定义事件ID
   * @returns 自定义事件
   */
  public getCustomEvent(id: string): CustomEvent | undefined {
    return this.customEvents.get(id);
  }
  
  /**
   * 获取所有自定义事件
   * @returns 自定义事件列表
   */
  public getCustomEvents(): CustomEvent[] {
    return Array.from(this.customEvents.values());
  }

  /**
   * 获取到输入的连接
   * @param nodeId 节点ID
   * @param inputName 输入插槽名称
   * @returns 连接信息
   */
  public getConnectionToInput(nodeId: string, inputName: string): { outputNodeId: string; outputSocketName: string } | null {
    const node = this.getNode(nodeId);
    if (!node) {
      return null;
    }

    const input = node.getInput(inputName);
    if (!input || !input.connectedNodeId || !input.connectedSocketName) {
      return null;
    }

    return {
      outputNodeId: input.connectedNodeId,
      outputSocketName: input.connectedSocketName
    };
  }

  /**
   * 清空图形
   */
  public clear(): void {
    // 断开所有节点连接
    for (const node of this.nodes.values()) {
      node.disconnectAll();
    }
    
    // 清空节点映射
    this.nodes.clear();
    
    // 清空变量映射
    this.variables.clear();
    
    // 清空自定义事件映射
    this.customEvents.clear();
    
    // 更新修改时间
    this.updatedAt = new Date();
    
    // 触发清空事件
    this.emit('cleared');
  }
  
  /**
   * 序列化为JSON
   * @returns 图形JSON数据
   */
  public toJSON(): GraphJSON {
    // 序列化节点
    const nodes = this.getNodes().map(node => {
      // TODO: 实现节点序列化
      return {
        id: node.id,
        type: node.type,
        metadata: node.metadata,
        parameters: {},
        flows: {}
      };
    });
    
    // 序列化变量
    const variables = this.getVariables().map(variable => {
      return {
        id: variable.id,
        name: variable.name,
        type: variable.type,
        value: variable.value,
        description: variable.description,
        constant: variable.constant,
        global: variable.global
      };
    });
    
    // 序列化自定义事件
    const customEvents = this.getCustomEvents().map(event => {
      return {
        id: event.id,
        name: event.name,
        parameterTypes: event.parameterTypes,
        description: event.description
      };
    });
    
    // 创建图形JSON数据
    const json: GraphJSON = {
      version: '1.0',
      name: this.name,
      description: this.description,
      nodes: nodes,
      variables: variables,
      customEvents: customEvents,
      metadata: {
        createdAt: this.createdAt.toISOString(),
        updatedAt: this.updatedAt.toISOString()
      }
    };
    
    return json;
  }
  
  /**
   * 从JSON创建图形
   * @param json 图形JSON数据
   * @returns 图形实例
   */
  public static fromJSON(json: GraphJSON): Graph {
    // TODO: 实现从JSON创建图形
    const graph = new Graph({
      id: json.name || 'graph',
      name: json.name,
      description: json.description
    });
    
    return graph;
  }
}
