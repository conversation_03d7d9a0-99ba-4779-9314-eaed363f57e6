/**
 * 视觉脚本AI模型节点
 * 提供对不同AI模型的支持
 */
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { AIModelManager } from '../../ai/AIModelManager';
import { AIModelType } from '../../ai/AIModelType';
import { AIModelConfig } from '../../ai/AIModelConfig';
import { AIModelLoadOptions } from '../../ai/AIModelLoadOptions';
import { IAIModel, TextGenerationOptions, ImageGenerationOptions } from '../../ai/models/IAIModel';

/**
 * 加载AI模型节点
 * 加载指定类型的AI模型
 */
export class LoadAIModelNode extends AsyncNode {

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'modelType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '模型类型',
      defaultValue: AIModelType.GPT
    });

    this.addInput({
      name: 'config',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '模型配置',
      defaultValue: {}
    });

    this.addInput({
      name: 'options',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '加载选项',
      defaultValue: {}
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '加载成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '加载失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'model',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '加载的模型'
    });

    this.addOutput({
      name: 'progress',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '加载进度 (0-1)'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const modelType = this.getInputValue('modelType') as string;
    const config = this.getInputValue('config') as object;
    const options = this.getInputValue('options') as object;

    // 获取AI模型管理器
    const aiModelManager = this.context.world.getSystem(AIModelManager);
    if (!aiModelManager) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 创建加载选项
      const loadOptions: AIModelLoadOptions = {
        ...options as AIModelLoadOptions,
        onProgress: (progress: number) => {
          this.setOutputValue('progress', progress);
        }
      };

      // 加载模型
      const model = await aiModelManager.loadModel(
        modelType as AIModelType,
        config as AIModelConfig,
        loadOptions
      );

      if (model) {
        // 设置输出值
        this.setOutputValue('model', model);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('加载AI模型失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 文本生成节点
 * 使用AI模型生成文本
 */
export class TextGenerationNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'model',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'AI模型'
    });

    this.addInput({
      name: 'prompt',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '提示文本',
      defaultValue: '你好，请生成一段文本。'
    });

    this.addInput({
      name: 'maxTokens',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大令牌数',
      defaultValue: 100
    });

    this.addInput({
      name: 'temperature',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '温度 (0-1)',
      defaultValue: 0.7
    });

    this.addInput({
      name: 'stream',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否使用流式响应',
      defaultValue: false
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '生成成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '生成失败'
    });

    this.addOutput({
      name: 'stream',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '流式响应'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '生成的文本'
    });

    this.addOutput({
      name: 'streamText',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '流式响应文本'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const model = this.getInputValue('model') as IAIModel;
    const prompt = this.getInputValue('prompt') as string;
    const maxTokens = this.getInputValue('maxTokens') as number;
    const temperature = this.getInputValue('temperature') as number;
    const stream = this.getInputValue('stream') as boolean;

    // 检查输入值是否有效
    if (!model || !prompt) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 创建生成选项
      const options: TextGenerationOptions = {
        maxTokens,
        temperature,
        stream
      };

      // 如果使用流式响应，添加回调
      if (stream) {
        options.onStream = (text: string) => {
          this.setOutputValue('streamText', text);
          this.triggerFlow('stream');
        };
      }

      // 生成文本
      const result = await model.generateText(prompt, options);

      if (result) {
        // 设置输出值
        this.setOutputValue('text', result);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('生成文本失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 图像生成节点
 * 使用AI模型生成图像
 */
export class ImageGenerationNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'model',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'AI模型'
    });

    this.addInput({
      name: 'prompt',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '提示文本'
    });

    this.addInput({
      name: 'width',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '图像宽度',
      defaultValue: 512
    });

    this.addInput({
      name: 'height',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '图像高度',
      defaultValue: 512
    });

    this.addInput({
      name: 'steps',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '生成步数',
      defaultValue: 30
    });

    this.addInput({
      name: 'guidanceScale',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '引导比例',
      defaultValue: 7.5
    });

    this.addInput({
      name: 'negativePrompt',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '负面提示',
      defaultValue: ''
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '生成成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '生成失败'
    });

    this.addOutput({
      name: 'progress',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '生成进度'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'image',
      type: SocketType.DATA,
      dataType: 'blob',
      direction: SocketDirection.OUTPUT,
      description: '生成的图像'
    });

    this.addOutput({
      name: 'progressValue',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '生成进度 (0-1)'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const model = this.getInputValue('model') as IAIModel;
    const prompt = this.getInputValue('prompt') as string;
    const width = this.getInputValue('width') as number;
    const height = this.getInputValue('height') as number;
    const steps = this.getInputValue('steps') as number;
    const guidanceScale = this.getInputValue('guidanceScale') as number;
    const negativePrompt = this.getInputValue('negativePrompt') as string;

    // 检查输入值是否有效
    if (!model || !prompt) {
      this.triggerFlow('fail');
      return false;
    }

    // 检查模型是否支持图像生成
    if (!model.generateImage) {
      console.error('模型不支持图像生成');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 创建生成选项
      const options: ImageGenerationOptions = {
        width,
        height,
        steps,
        guidanceScale,
        negativePrompt,
        onProgress: (progress: number) => {
          this.setOutputValue('progressValue', progress);
          this.triggerFlow('progress');
        }
      };

      // 生成图像
      const result = await model.generateImage(prompt, options);

      if (result) {
        // 设置输出值
        this.setOutputValue('image', result);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('生成图像失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 情感分析节点
 * 分析文本情感
 */
export class EmotionAnalysisNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'model',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'AI模型'
    });

    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要分析的文本'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'primaryEmotion',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '主要情感'
    });

    this.addOutput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '情感强度 (0-1)'
    });

    this.addOutput({
      name: 'scores',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '情感分数映射'
    });

    this.addOutput({
      name: 'confidence',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '置信度 (0-1)'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const model = this.getInputValue('model') as IAIModel;
    const text = this.getInputValue('text') as string;

    // 检查输入值是否有效
    if (!model || !text) {
      this.triggerFlow('fail');
      return false;
    }

    // 检查模型是否支持情感分析
    if (!model.analyzeEmotion) {
      console.error('模型不支持情感分析');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 分析情感
      const result = await model.analyzeEmotion(text);

      if (result) {
        // 设置输出值
        this.setOutputValue('primaryEmotion', result.primaryEmotion);
        this.setOutputValue('intensity', result.intensity);
        this.setOutputValue('scores', result.scores);
        this.setOutputValue('confidence', result.confidence);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('分析情感失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 注册AI模型节点
 * @param registry 节点注册表
 */
export function registerAIModelNodes(registry: NodeRegistry): void {
  // 注册加载AI模型节点
  registry.registerNodeType({
    type: 'ai/model/load',
    category: NodeCategory.AI,
    constructor: LoadAIModelNode,
    label: '加载AI模型',
    description: '加载指定类型的AI模型',
    icon: 'model',
    color: '#673AB7',
    tags: ['ai', 'model', 'load']
  });

  // 注册文本生成节点
  registry.registerNodeType({
    type: 'ai/model/generateText',
    category: NodeCategory.AI,
    constructor: TextGenerationNode,
    label: '生成文本',
    description: '使用AI模型生成文本',
    icon: 'text',
    color: '#673AB7',
    tags: ['ai', 'model', 'text', 'generate']
  });

  // 注册图像生成节点
  registry.registerNodeType({
    type: 'ai/model/generateImage',
    category: NodeCategory.AI,
    constructor: ImageGenerationNode,
    label: '生成图像',
    description: '使用AI模型生成图像',
    icon: 'image',
    color: '#673AB7',
    tags: ['ai', 'model', 'image', 'generate']
  });

  // 注册情感分析节点
  registry.registerNodeType({
    type: 'ai/emotion/analyze',
    category: NodeCategory.AI,
    constructor: EmotionAnalysisNode,
    label: '情感分析',
    description: '分析文本情感',
    icon: 'emotion',
    color: '#673AB7',
    tags: ['ai', 'emotion', 'analyze']
  });
}
