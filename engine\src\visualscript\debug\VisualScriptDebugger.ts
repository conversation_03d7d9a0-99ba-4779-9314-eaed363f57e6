/**
 * 视觉脚本调试器
 * 提供视觉脚本的调试功能，包括断点、单步执行、变量监视等
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Graph } from '../graph/Graph';
import { Node } from '../nodes/Node';
import { VisualScriptEngine } from '../VisualScriptEngine';

/**
 * 断点类型
 */
export enum BreakpointType {
  /**
   * 普通断点
   */
  NORMAL = 'normal',
  
  /**
   * 条件断点
   */
  CONDITIONAL = 'conditional'
}

/**
 * 断点信息
 */
export interface Breakpoint {
  /**
   * 断点ID
   */
  id: string;
  
  /**
   * 节点ID
   */
  nodeId: string;
  
  /**
   * 图ID
   */
  graphId: string;
  
  /**
   * 断点类型
   */
  type: BreakpointType;
  
  /**
   * 条件表达式（仅条件断点）
   */
  condition?: string;
  
  /**
   * 是否启用
   */
  enabled: boolean;
}

/**
 * 执行状态
 */
export enum ExecutionState {
  /**
   * 运行中
   */
  RUNNING = 'running',
  
  /**
   * 暂停
   */
  PAUSED = 'paused',
  
  /**
   * 停止
   */
  STOPPED = 'stopped'
}

/**
 * 执行步骤类型
 */
export enum StepType {
  /**
   * 单步执行（进入子图）
   */
  STEP_INTO = 'step_into',
  
  /**
   * 单步执行（不进入子图）
   */
  STEP_OVER = 'step_over',
  
  /**
   * 单步执行（跳出当前子图）
   */
  STEP_OUT = 'step_out'
}

/**
 * 执行路径项
 */
export interface ExecutionPathItem {
  /**
   * 节点ID
   */
  nodeId: string;
  
  /**
   * 图ID
   */
  graphId: string;
  
  /**
   * 时间戳
   */
  timestamp: number;
}

/**
 * 视觉脚本调试器
 */
export class VisualScriptDebugger extends EventEmitter {
  /**
   * 断点列表
   */
  private breakpoints: Map<string, Breakpoint> = new Map();
  
  /**
   * 当前执行状态
   */
  private executionState: ExecutionState = ExecutionState.STOPPED;
  
  /**
   * 当前暂停的节点
   */
  private currentNode: Node | null = null;
  
  /**
   * 当前暂停的图
   */
  private currentGraph: Graph | null = null;
  
  /**
   * 执行路径历史
   */
  private executionPath: ExecutionPathItem[] = [];
  
  /**
   * 最大执行路径历史长度
   */
  private maxExecutionPathLength: number = 100;
  
  /**
   * 变量监视列表
   */
  private watchedVariables: Set<string> = new Set();
  
  /**
   * 视觉脚本引擎
   */
  private engine: VisualScriptEngine;
  
  /**
   * 构造函数
   * @param engine 视觉脚本引擎
   */
  constructor(engine: VisualScriptEngine) {
    super();
    this.engine = engine;
    
    // 监听引擎事件
    this.engine.on('nodeBeforeExecute', this.onNodeBeforeExecute.bind(this));
    this.engine.on('nodeAfterExecute', this.onNodeAfterExecute.bind(this));
    this.engine.on('graphStart', this.onGraphStart.bind(this));
    this.engine.on('graphEnd', this.onGraphEnd.bind(this));
  }
  
  /**
   * 添加断点
   * @param nodeId 节点ID
   * @param graphId 图ID
   * @param type 断点类型
   * @param condition 条件表达式
   * @returns 断点ID
   */
  public addBreakpoint(nodeId: string, graphId: string, type: BreakpointType = BreakpointType.NORMAL, condition?: string): string {
    const id = `${graphId}:${nodeId}`;
    
    const breakpoint: Breakpoint = {
      id,
      nodeId,
      graphId,
      type,
      condition,
      enabled: true
    };
    
    this.breakpoints.set(id, breakpoint);
    
    this.emit('breakpointAdded', breakpoint);
    
    return id;
  }
  
  /**
   * 移除断点
   * @param id 断点ID
   * @returns 是否成功
   */
  public removeBreakpoint(id: string): boolean {
    const breakpoint = this.breakpoints.get(id);
    
    if (breakpoint) {
      this.breakpoints.delete(id);
      this.emit('breakpointRemoved', breakpoint);
      return true;
    }
    
    return false;
  }
  
  /**
   * 启用/禁用断点
   * @param id 断点ID
   * @param enabled 是否启用
   * @returns 是否成功
   */
  public setBreakpointEnabled(id: string, enabled: boolean): boolean {
    const breakpoint = this.breakpoints.get(id);
    
    if (breakpoint) {
      breakpoint.enabled = enabled;
      this.emit('breakpointUpdated', breakpoint);
      return true;
    }
    
    return false;
  }
  
  /**
   * 更新断点条件
   * @param id 断点ID
   * @param condition 条件表达式
   * @returns 是否成功
   */
  public updateBreakpointCondition(id: string, condition: string): boolean {
    const breakpoint = this.breakpoints.get(id);
    
    if (breakpoint && breakpoint.type === BreakpointType.CONDITIONAL) {
      breakpoint.condition = condition;
      this.emit('breakpointUpdated', breakpoint);
      return true;
    }
    
    return false;
  }
  
  /**
   * 获取所有断点
   * @returns 断点列表
   */
  public getBreakpoints(): Breakpoint[] {
    return Array.from(this.breakpoints.values());
  }
  
  /**
   * 获取节点的断点
   * @param nodeId 节点ID
   * @param graphId 图ID
   * @returns 断点
   */
  public getBreakpoint(nodeId: string, graphId: string): Breakpoint | undefined {
    const id = `${graphId}:${nodeId}`;
    return this.breakpoints.get(id);
  }
  
  /**
   * 继续执行
   */
  public continue(): void {
    if (this.executionState === ExecutionState.PAUSED) {
      this.executionState = ExecutionState.RUNNING;
      this.emit('continued');
    }
  }
  
  /**
   * 暂停执行
   */
  public pause(): void {
    if (this.executionState === ExecutionState.RUNNING) {
      this.executionState = ExecutionState.PAUSED;
      this.emit('paused');
    }
  }
  
  /**
   * 停止执行
   */
  public stop(): void {
    this.executionState = ExecutionState.STOPPED;
    this.currentNode = null;
    this.currentGraph = null;
    this.executionPath = [];
    this.emit('stopped');
  }
  
  /**
   * 单步执行
   * @param type 步骤类型
   */
  public step(type: StepType): void {
    if (this.executionState === ExecutionState.PAUSED) {
      this.emit('step', type);
      this.executionState = ExecutionState.RUNNING;
    }
  }
  
  /**
   * 添加变量监视
   * @param variableName 变量名
   */
  public addWatch(variableName: string): void {
    this.watchedVariables.add(variableName);
    this.emit('watchAdded', variableName);
  }
  
  /**
   * 移除变量监视
   * @param variableName 变量名
   */
  public removeWatch(variableName: string): void {
    this.watchedVariables.delete(variableName);
    this.emit('watchRemoved', variableName);
  }
  
  /**
   * 获取所有监视变量
   * @returns 监视变量列表
   */
  public getWatches(): string[] {
    return Array.from(this.watchedVariables);
  }

  /**
   * 获取当前暂停的节点
   * @returns 当前节点
   */
  public getCurrentNode(): Node | null {
    return this.currentNode;
  }

  /**
   * 获取当前暂停的图
   * @returns 当前图
   */
  public getCurrentGraph(): Graph | null {
    return this.currentGraph;
  }
  
  /**
   * 获取变量值
   * @param variableName 变量名
   * @returns 变量值
   */
  public getVariableValue(variableName: string): any {
    if (this.currentGraph) {
      const variable = this.currentGraph.getVariable(variableName);
      return variable ? variable.value : undefined;
    }

    return undefined;
  }

  /**
   * 设置变量值
   * @param variableName 变量名
   * @param value 变量值
   * @returns 是否成功
   */
  public setVariableValue(variableName: string, value: any): boolean {
    if (this.currentGraph) {
      const variable = this.currentGraph.getVariable(variableName);
      if (variable) {
        variable.value = value;
        this.emit('variableChanged', { name: variableName, value });
        return true;
      }
    }

    return false;
  }
  
  /**
   * 获取执行路径
   * @returns 执行路径
   */
  public getExecutionPath(): ExecutionPathItem[] {
    return [...this.executionPath];
  }
  
  /**
   * 清除执行路径
   */
  public clearExecutionPath(): void {
    this.executionPath = [];
    this.emit('executionPathCleared');
  }
  
  /**
   * 设置最大执行路径长度
   * @param length 长度
   */
  public setMaxExecutionPathLength(length: number): void {
    this.maxExecutionPathLength = length;
    
    // 裁剪当前执行路径
    if (this.executionPath.length > this.maxExecutionPathLength) {
      this.executionPath = this.executionPath.slice(-this.maxExecutionPathLength);
    }
  }
  
  /**
   * 节点执行前事件处理
   * @param node 节点
   * @param graph 图
   */
  private onNodeBeforeExecute(node: Node, graph: Graph): void {
    // 记录执行路径
    this.addExecutionPathItem(node.id, graph.id);
    
    // 检查断点
    const breakpoint = this.getBreakpoint(node.id, graph.id);
    
    if (breakpoint && breakpoint.enabled) {
      let shouldBreak = breakpoint.type === BreakpointType.NORMAL;
      
      // 检查条件断点
      if (breakpoint.type === BreakpointType.CONDITIONAL && breakpoint.condition) {
        try {
          // 创建变量上下文对象
          const variables: { [key: string]: any } = {};
          for (const variable of graph.getVariables()) {
            variables[variable.name] = variable.value;
          }

          // 使用Function构造函数创建一个函数来评估条件
          const conditionFunc = new Function('variables', 'graph', 'node', `with (variables) { return ${breakpoint.condition}; }`);
          shouldBreak = conditionFunc(variables, graph, node);
        } catch (error) {
          console.error(`条件断点表达式错误: ${error}`);
        }
      }
      
      if (shouldBreak) {
        this.currentNode = node;
        this.currentGraph = graph;
        this.executionState = ExecutionState.PAUSED;
        
        this.emit('breakpointHit', {
          breakpoint,
          node,
          graph
        });
      }
    }
  }
  
  /**
   * 节点执行后事件处理
   * @param _node 节点
   * @param graph 图
   * @param _result 执行结果
   */
  private onNodeAfterExecute(_node: Node, graph: Graph, _result: any): void {
    // 更新监视变量
    if (this.watchedVariables.size > 0) {
      const watchValues: { [key: string]: any } = {};

      for (const variableName of this.watchedVariables) {
        const variable = graph.getVariable(variableName);
        watchValues[variableName] = variable ? variable.value : undefined;
      }

      this.emit('watchUpdated', watchValues);
    }
  }
  
  /**
   * 图开始执行事件处理
   * @param graph 图
   */
  private onGraphStart(graph: Graph): void {
    this.emit('graphStarted', graph);
  }

  /**
   * 图结束执行事件处理
   * @param graph 图
   */
  private onGraphEnd(graph: Graph): void {
    this.emit('graphEnded', graph);
  }
  
  /**
   * 添加执行路径项
   * @param nodeId 节点ID
   * @param graphId 图ID
   */
  private addExecutionPathItem(nodeId: string, graphId: string): void {
    const item: ExecutionPathItem = {
      nodeId,
      graphId,
      timestamp: Date.now()
    };
    
    this.executionPath.push(item);
    
    // 限制执行路径长度
    if (this.executionPath.length > this.maxExecutionPathLength) {
      this.executionPath.shift();
    }
    
    this.emit('executionPathUpdated', this.executionPath);
  }
}
